#!/usr/bin/env python3
"""
Test script for the Payment Bot
This script helps verify that all dependencies are installed correctly
"""

def test_imports():
    """Test if all required modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        import telebot
        print("✅ telebot imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import telebot: {e}")
        return False
    
    try:
        import requests
        print("✅ requests imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import requests: {e}")
        return False
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        print("✅ PIL (Pillow) imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import PIL: {e}")
        return False
    
    try:
        import qrcode
        print("✅ qrcode imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import qrcode: {e}")
        return False
    
    return True

def test_config():
    """Test if configuration is properly set"""
    print("\n🔧 Testing configuration...")
    
    try:
        from config import BOT_TOKEN, KHQR_API_BASE_URL, BAKONG_API_URL
        
        if BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
            print("⚠️  Warning: BOT_TOKEN is not configured!")
            print("   Please edit config.py and set your actual bot token")
            return False
        else:
            print("✅ BOT_TOKEN is configured")
        
        print(f"✅ KHQR API URL: {KHQR_API_BASE_URL}")
        print(f"✅ Bakong API URL: {BAKONG_API_URL}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import config: {e}")
        return False

def test_api_connectivity():
    """Test if APIs are reachable"""
    print("\n🌐 Testing API connectivity...")
    
    try:
        import requests
        from config import KHQR_API_BASE_URL
        
        # Test KHQR API with a small amount
        test_url = f"{KHQR_API_BASE_URL}?amount=0.01&bakongid=test&merchantname=test"
        
        print(f"Testing: {KHQR_API_BASE_URL}")
        response = requests.get(test_url, timeout=10)
        
        if response.status_code in [200, 201]:
            print("✅ KHQR API is reachable")
            return True
        else:
            print(f"⚠️  KHQR API returned status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API connectivity test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🤖 Payment Bot Test Suite")
    print("=" * 40)
    
    tests_passed = 0
    total_tests = 3
    
    # Test imports
    if test_imports():
        tests_passed += 1
    
    # Test configuration
    if test_config():
        tests_passed += 1
    
    # Test API connectivity
    if test_api_connectivity():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 40)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Your bot should work correctly.")
        print("\nTo start the bot, run:")
        print("python payment_bot.py")
    else:
        print("❌ Some tests failed. Please fix the issues above before running the bot.")
        
        if tests_passed == 0:
            print("\n💡 Quick fix suggestions:")
            print("1. Install dependencies: pip install -r requirements.txt")
            print("2. Configure your bot token in config.py")

if __name__ == "__main__":
    main()
