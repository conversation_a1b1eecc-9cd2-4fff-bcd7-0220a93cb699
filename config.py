# Configuration file for the Payment Bot

# Telegram Bot Configuration
BOT_TOKEN = "**********************************************"  # Get this from @BotFather on Telegram

# Payment API Configuration
KHQR_API_BASE_URL = "https://api.kunchhunlichhean.org/khqr/create"
BAKONG_API_URL = "https://api-bakong.nbc.gov.kh/v1/check_transaction_by_md5"

# Payment Settings
BAKONG_ID = "chhunlichhean_kun@wing"
MERCHANT_NAME = "CHHEANSMM"

# API Authorization Token
BAKONG_AUTH_TOKEN = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjp7ImlkIjoiYzlhZDQ1MDAwODNkNDNjNiJ9LCJpYXQiOjE3NTE2OTA3MDMsImV4cCI6MTc1OTQ2NjcwM30.2CoTXA22afg-iveEdGkL8uusGjqIC64WXHum1Ae390k"

# QR Code Settings
QR_CODE_TIMEOUT = 180  # 3 minutes in seconds
CHECK_INTERVAL = 10    # Check every 10 seconds
MAX_CHECKS = 18        # Total checks (3 minutes / 10 seconds)

# Design Settings
CANVAS_WIDTH = 600
CANVAS_HEIGHT = 700
QR_SIZE = 400
HEADER_HEIGHT = 80
HEADER_COLOR = '#4CAF50'
