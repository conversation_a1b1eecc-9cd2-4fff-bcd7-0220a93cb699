import telebot
import requests
import time
import threading
from PIL import Image, ImageDraw, ImageFont
import qrcode
import io
import os
from datetime import datetime
from config import *

# Bot configuration
bot = telebot.TeleBot(BOT_TOKEN)

# Store active transactions
transactions = {}

# Welcome message and commands
@bot.message_handler(commands=['start'])
def send_welcome(message):
    welcome_text = """
🎉 Welcome to Payment Bot! 🎉

Available commands:
💰 /topup - Add money to your account
ℹ️ /help - Show this help message

Simply use /topup followed by the amount you want to add!
Example: /topup 0.01
"""
    bot.reply_to(message, welcome_text)

@bot.message_handler(commands=['help'])
def send_help(message):
    help_text = """
📋 Bot Commands:

💰 /topup - Start the payment process
   Usage: /topup <amount>
   Example: /topup 0.01

🔄 The bot will:
1. Generate a QR code for payment
2. Monitor the transaction automatically
3. Confirm when payment is received
4. QR codes expire after 3 minutes

Need help? Contact support!
"""
    bot.reply_to(message, help_text)

@bot.message_handler(commands=['topup'])
def handle_topup(message):
    try:
        # Extract amount from command
        command_parts = message.text.split()
        if len(command_parts) < 2:
            bot.reply_to(message, "Please specify an amount!\nExample: /topup 0.01")
            return
        
        amount = command_parts[1].strip()
        
        # Validate amount
        try:
            float_amount = float(amount)
            if float_amount <= 0:
                bot.reply_to(message, "Amount must be greater than 0!")
                return
        except ValueError:
            bot.reply_to(message, "Invalid amount! Please enter a numeric value.\nExample: /topup 0.01")
            return
        
        # Generate QR code
        generate_qr_code(message, amount)
        
    except Exception as e:
        bot.reply_to(message, f"An error occurred: {e}")

def generate_qr_code(message, amount):
    try:
        # Set up the API URL with the user-provided amount
        api_url = f"{KHQR_API_BASE_URL}?amount={amount}&bakongid={BAKONG_ID}&merchantname={MERCHANT_NAME}"
        
        # Make the GET request to the API
        response = requests.get(api_url)
        
        if response.status_code in [200, 201]:
            response_data = response.json()
            qr_code_url = response_data.get('qr')
            md5 = response_data.get('md5')
            
            if qr_code_url and md5:
                # Create enhanced QR code design
                enhanced_qr_image = create_enhanced_qr_design(qr_code_url, amount)
                
                # Send the enhanced QR code image with PHP-style caption
                caption = f"""
🏦 KHQR Payment - CamboPay

💰 Amount: ${amount} USD
⏰ Valid for: 3 minutes
📱 Scan with your banking app

✅ Supported by all major banks
🔄 Monitoring payment status...
"""
                
                msg = bot.send_photo(
                    message.chat.id, 
                    enhanced_qr_image, 
                    caption=caption
                )
                
                # Store the transaction
                transactions[message.chat.id] = {
                    "md5": md5,
                    "message_id": msg.message_id,
                    "timestamp": time.time(),
                    "amount": amount
                }
                
                # Start automatic checking
                threading.Thread(
                    target=auto_check_transaction, 
                    args=(message.chat.id, md5, amount)
                ).start()
                
                # Start a timer to delete the QR code after 3 minutes
                threading.Thread(
                    target=delete_qr_after_timeout, 
                    args=(message.chat.id,)
                ).start()
            else:
                bot.reply_to(message, "❌ Failed to generate QR code: Missing QR code URL or MD5 in response.")
        else:
            bot.reply_to(message, f"❌ Failed to make the request. Status code: {response.status_code}")
    except Exception as e:
        bot.reply_to(message, f"❌ An error occurred: {e}")

def create_enhanced_qr_design(qr_url, amount):
    try:
        # Download the QR code image
        qr_response = requests.get(qr_url)
        qr_image = Image.open(io.BytesIO(qr_response.content))

        # Create a new image with enhanced design (similar to PHP version)
        canvas_width = 400
        canvas_height = 600
        canvas = Image.new('RGB', (canvas_width, canvas_height), '#f8f9fa')
        draw = ImageDraw.Draw(canvas)

        # Draw main container with rounded corners effect
        container_margin = 20

        # Draw white background container
        draw.rectangle([
            container_margin, container_margin,
            canvas_width - container_margin, canvas_height - container_margin
        ], fill='white', outline='#e0e0e0', width=1)

        # Load fonts
        try:
            title_font = ImageFont.truetype("arial.ttf", 18)
            amount_font = ImageFont.truetype("arial.ttf", 32)
            subtitle_font = ImageFont.truetype("arial.ttf", 14)
            small_font = ImageFont.truetype("arial.ttf", 12)
        except:
            title_font = ImageFont.load_default()
            amount_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
            small_font = ImageFont.load_default()

        # Header section - Company name and countdown
        header_y = container_margin + 20

        # Company name (similar to PHP version)
        company_text = "CHHEAN-SMM.NET - By CamboPay"
        company_bbox = draw.textbbox((0, 0), company_text, font=subtitle_font)
        company_width = company_bbox[2] - company_bbox[0]
        draw.text(
            ((canvas_width - company_width) // 2, header_y),
            company_text,
            fill='#333333',
            font=subtitle_font
        )

        # Countdown timer (3:00)
        countdown_text = "3:00"
        countdown_bbox = draw.textbbox((0, 0), countdown_text, font=title_font)
        countdown_width = countdown_bbox[2] - countdown_bbox[0]
        draw.text(
            ((canvas_width - countdown_width) // 2, header_y + 25),
            countdown_text,
            fill='#666666',
            font=title_font
        )

        # Amount display section (similar to PHP version)
        amount_y = header_y + 60

        # Currency symbol
        currency_text = "$"
        draw.text(
            (canvas_width // 2 - 40, amount_y),
            currency_text,
            fill='#333333',
            font=amount_font
        )

        # Amount value
        amount_text = str(amount)
        draw.text(
            (canvas_width // 2 - 10, amount_y),
            amount_text,
            fill='#333333',
            font=amount_font
        )

        # QR Code section
        qr_y = amount_y + 60
        qr_size = 200  # Smaller QR code like in PHP version
        qr_image = qr_image.resize((qr_size, qr_size), Image.Resampling.LANCZOS)
        qr_x = (canvas_width - qr_size) // 2

        # Draw QR code background (white square with border)
        qr_bg_margin = 10
        draw.rectangle([
            qr_x - qr_bg_margin, qr_y - qr_bg_margin,
            qr_x + qr_size + qr_bg_margin, qr_y + qr_size + qr_bg_margin
        ], fill='white', outline='#e0e0e0', width=2)

        canvas.paste(qr_image, (qr_x, qr_y))

        # Add KHQR logo overlay in center of QR code (similar to PHP version)
        try:
            # Create a simple logo placeholder (you can replace with actual logo)
            logo_size = 40
            logo_x = qr_x + (qr_size - logo_size) // 2
            logo_y = qr_y + (qr_size - logo_size) // 2

            # Draw white circle background for logo
            draw.ellipse([
                logo_x - 5, logo_y - 5,
                logo_x + logo_size + 5, logo_y + logo_size + 5
            ], fill='white', outline='#333333', width=2)

            # Draw "KHQR" text as logo
            logo_font = ImageFont.truetype("arial.ttf", 10) if title_font else ImageFont.load_default()
            logo_text = "KHQR"
            logo_bbox = draw.textbbox((0, 0), logo_text, font=logo_font)
            logo_text_width = logo_bbox[2] - logo_bbox[0]
            draw.text(
                (logo_x + (logo_size - logo_text_width) // 2, logo_y + 15),
                logo_text,
                fill='#333333',
                font=logo_font
            )
        except:
            pass  # Skip logo if there's an error

        # Footer section with payment instructions
        footer_y = qr_y + qr_size + 30

        # Payment instructions (similar to PHP style)
        instruction_text = "Scan QR code with your banking app"
        instruction_bbox = draw.textbbox((0, 0), instruction_text, font=subtitle_font)
        instruction_width = instruction_bbox[2] - instruction_bbox[0]
        draw.text(
            ((canvas_width - instruction_width) // 2, footer_y),
            instruction_text,
            fill='#666666',
            font=subtitle_font
        )

        # Supported banks text
        banks_text = "Supported by all major banks in Cambodia"
        banks_bbox = draw.textbbox((0, 0), banks_text, font=small_font)
        banks_width = banks_bbox[2] - banks_bbox[0]
        draw.text(
            ((canvas_width - banks_width) // 2, footer_y + 25),
            banks_text,
            fill='#999999',
            font=small_font
        )

        # Add timestamp at bottom
        timestamp = datetime.now().strftime("%H:%M:%S")
        timestamp_text = f"Generated at {timestamp}"
        timestamp_bbox = draw.textbbox((0, 0), timestamp_text, font=small_font)
        timestamp_width = timestamp_bbox[2] - timestamp_bbox[0]
        draw.text(
            ((canvas_width - timestamp_width) // 2, footer_y + 50),
            timestamp_text,
            fill='#cccccc',
            font=small_font
        )

        # Add subtle border around the entire design
        draw.rectangle([
            container_margin + 5, container_margin + 5,
            canvas_width - container_margin - 5, canvas_height - container_margin - 5
        ], outline='#f0f0f0', width=1)

        # Convert to bytes for sending
        img_byte_arr = io.BytesIO()
        canvas.save(img_byte_arr, format='PNG', quality=95)
        img_byte_arr.seek(0)

        return img_byte_arr

    except Exception as e:
        print(f"Error creating enhanced QR design: {e}")
        # Fallback to original QR code
        qr_response = requests.get(qr_url)
        return io.BytesIO(qr_response.content)

# Auto-check transaction status
def auto_check_transaction(user_id, md5, amount):
    payload = {"md5": md5}
    headers = {
        'Content-Type': 'application/json',
        'Authorization': BAKONG_AUTH_TOKEN
    }

    check_count = 0

    while check_count < MAX_CHECKS and user_id in transactions:
        try:
            response = requests.post(BAKONG_API_URL, json=payload, headers=headers)
            if response.status_code in [200, 201]:
                response_data = response.json()
                if response_data.get('responseCode') == 0:  # Success
                    # Send success message with emoji
                    success_message = f"""
✅ Payment Confirmed! ✅

💰 Amount: ${amount} USD
✨ Status: Successfully processed
🎉 Thank you for your payment!

Your account has been updated.
"""
                    bot.send_message(user_id, success_message)

                    # Delete the QR code message
                    if user_id in transactions:
                        try:
                            bot.delete_message(
                                chat_id=user_id,
                                message_id=transactions[user_id]["message_id"]
                            )
                        except:
                            pass  # Message might already be deleted
                        transactions.pop(user_id, None)
                    return

            time.sleep(CHECK_INTERVAL)  # Check every 10 seconds
            check_count += 1

        except Exception as e:
            print(f"Error checking transaction: {e}")
            time.sleep(CHECK_INTERVAL)
            check_count += 1

# Delete QR code after timeout
def delete_qr_after_timeout(user_id):
    time.sleep(QR_CODE_TIMEOUT)  # Wait for 3 minutes
    transaction = transactions.get(user_id)
    if transaction:
        try:
            bot.delete_message(chat_id=user_id, message_id=transaction["message_id"])
        except:
            pass  # Message might already be deleted

        transactions.pop(user_id, None)

        timeout_message = """
⏰ QR Code Expired

Your QR code has expired after 3 minutes.
Please use /topup again to generate a new payment code.

Example: /topup 0.01
"""
        bot.send_message(user_id, timeout_message)

# Handle any other messages
@bot.message_handler(func=lambda msg: True)
def handle_other_messages(message):
    help_text = """
❓ Unknown command!

Available commands:
💰 /topup <amount> - Generate payment QR code
ℹ️ /help - Show help information
🏠 /start - Show welcome message

Example: /topup 0.01
"""
    bot.reply_to(message, help_text)

# Start the bot
if __name__ == "__main__":
    print("🤖 Payment Bot is starting...")
    print("📱 Bot is ready to receive payments!")
    try:
        bot.infinity_polling()
    except Exception as e:
        print(f"❌ Bot error: {e}")
        print("🔄 Restarting bot...")
