import telebot
import requests
import time
import threading
from PIL import Image, ImageDraw, ImageFont
import qrcode
import io
import os
from datetime import datetime
from config import *

# Bot configuration
bot = telebot.TeleBot(BOT_TOKEN)

# Store active transactions
transactions = {}

# Welcome message and commands
@bot.message_handler(commands=['start'])
def send_welcome(message):
    welcome_text = """
🎉 Welcome to Payment Bot! 🎉

Available commands:
💰 /topup - Add money to your account
ℹ️ /help - Show this help message

Simply use /topup followed by the amount you want to add!
Example: /topup 0.01
"""
    bot.reply_to(message, welcome_text)

@bot.message_handler(commands=['help'])
def send_help(message):
    help_text = """
📋 Bot Commands:

💰 /topup - Start the payment process
   Usage: /topup <amount>
   Example: /topup 0.01

🔄 The bot will:
1. Generate a QR code for payment
2. Monitor the transaction automatically
3. Confirm when payment is received
4. QR codes expire after 3 minutes

Need help? Contact support!
"""
    bot.reply_to(message, help_text)

@bot.message_handler(commands=['topup'])
def handle_topup(message):
    try:
        # Extract amount from command
        command_parts = message.text.split()
        if len(command_parts) < 2:
            bot.reply_to(message, "Please specify an amount!\nExample: /topup 0.01")
            return
        
        amount = command_parts[1].strip()
        
        # Validate amount
        try:
            float_amount = float(amount)
            if float_amount <= 0:
                bot.reply_to(message, "Amount must be greater than 0!")
                return
        except ValueError:
            bot.reply_to(message, "Invalid amount! Please enter a numeric value.\nExample: /topup 0.01")
            return
        
        # Generate QR code
        generate_qr_code(message, amount)
        
    except Exception as e:
        bot.reply_to(message, f"An error occurred: {e}")

def generate_qr_code(message, amount):
    try:
        # Set up the API URL with the user-provided amount
        api_url = f"{KHQR_API_BASE_URL}?amount={amount}&bakongid={BAKONG_ID}&merchantname={MERCHANT_NAME}"
        
        # Make the GET request to the API
        response = requests.get(api_url)
        
        if response.status_code in [200, 201]:
            response_data = response.json()
            qr_code_url = response_data.get('qr')
            md5 = response_data.get('md5')
            
            if qr_code_url and md5:
                # Create enhanced QR code design
                enhanced_qr_image = create_enhanced_qr_design(qr_code_url, amount)
                
                # Send the enhanced QR code image with PHP-style caption
                caption = f"""
🏦 KHQR Payment - CamboPay

💰 Amount: ${amount} USD
⏰ Valid for: 3 minutes
📱 Scan with your banking app

✅ Supported by all major banks
🔄 Monitoring payment status...
"""
                
                msg = bot.send_photo(
                    message.chat.id, 
                    enhanced_qr_image, 
                    caption=caption
                )
                
                # Store the transaction
                transactions[message.chat.id] = {
                    "md5": md5,
                    "message_id": msg.message_id,
                    "timestamp": time.time(),
                    "amount": amount
                }
                
                # Start automatic checking
                threading.Thread(
                    target=auto_check_transaction, 
                    args=(message.chat.id, md5, amount)
                ).start()
                
                # Start a timer to delete the QR code after 3 minutes
                threading.Thread(
                    target=delete_qr_after_timeout, 
                    args=(message.chat.id,)
                ).start()
            else:
                bot.reply_to(message, "❌ Failed to generate QR code: Missing QR code URL or MD5 in response.")
        else:
            bot.reply_to(message, f"❌ Failed to make the request. Status code: {response.status_code}")
    except Exception as e:
        bot.reply_to(message, f"❌ An error occurred: {e}")

def create_enhanced_qr_design(qr_url, amount):
    try:
        # Download the QR code image
        qr_response = requests.get(qr_url)
        qr_image = Image.open(io.BytesIO(qr_response.content))

        # Create high-resolution canvas (1080p quality)
        scale_factor = 4  # 4x scale for high resolution
        canvas_width = 252 * scale_factor  # From qrcode.css .qrcode-body width
        canvas_height = 351 * scale_factor  # From qrcode.css .qrcode-body height

        # Create background (transparent/gray like PHP)
        bg_canvas = Image.new('RGBA', (canvas_width + 40*scale_factor, canvas_height + 80*scale_factor), (217, 217, 217, 0))

        # Create main white card with shadow effect
        card = Image.new('RGBA', (canvas_width, canvas_height), (255, 255, 255, 255))
        draw = ImageDraw.Draw(card)

        # Load fonts to match PHP design (scaled for high resolution)
        try:
            company_font = ImageFont.truetype("arial.ttf", 12 * scale_factor)  # .qrcode-name font-size: 12px
            amount_font = ImageFont.truetype("arial.ttf", 18 * scale_factor)   # .qrcode-amount font-size: 18px
        except:
            company_font = ImageFont.load_default()
            amount_font = ImageFont.load_default()

        # 1. Draw red header (qrhrader) - height: 36px, background: #E1232E
        header_height = 36 * scale_factor
        draw.rectangle([0, 0, canvas_width, header_height], fill='#E1232E')

        # 2. Add KHQR logo in header center
        try:
            # Try to load the actual KHQR logo
            khqr_logo = Image.open("khqr logo-200h.png")
            # Resize to match CSS: width: 48px (--dl-size-size-small) scaled
            logo_width = 48 * scale_factor
            logo_height = int(khqr_logo.height * (logo_width / khqr_logo.width))
            khqr_logo = khqr_logo.resize((logo_width, logo_height), Image.Resampling.LANCZOS)

            # Center logo in header
            logo_x = (canvas_width - logo_width) // 2
            logo_y = (header_height - logo_height) // 2

            # Convert to RGBA if needed
            if khqr_logo.mode != 'RGBA':
                khqr_logo = khqr_logo.convert('RGBA')

            card.paste(khqr_logo, (logo_x, logo_y), khqr_logo)
        except:
            # Fallback: draw "KHQR" text
            draw.text((canvas_width//2 - 20*scale_factor, header_height//2 - 8*scale_factor), "KHQR", fill='white', font=company_font)

        # 3. Company name (top: 55px, left: 19px, font-size: 12px) - scaled
        company_text = "CHHEAN-SMM.NET - By CamboPay"
        company_y = 55 * scale_factor
        company_x = 19 * scale_factor
        draw.text((company_x, company_y), company_text, fill='black', font=company_font)

        # 4. Currency and amount (top: 70px, left: 18px and 31px, font-size: 18px) - scaled
        currency_y = 70 * scale_factor
        currency_x = 18 * scale_factor
        amount_x = 31 * scale_factor

        draw.text((currency_x, currency_y), "$", fill='black', font=amount_font)
        draw.text((amount_x, currency_y), str(amount), fill='black', font=amount_font)

        # 5. Dotted line separator (top: 99px, border-top: 1px dotted #dadada) - scaled
        line_y = 99 * scale_factor
        for x in range(0, canvas_width, 4 * scale_factor):
            draw.point((x, line_y), fill='#dadada')

        # 6. QR Code section (top: 18px from line, 200x200px) - scaled
        qr_y = line_y + 18 * scale_factor
        qr_size = 200 * scale_factor  # From CSS .qrcode width/height: 200px

        # Resize QR code to match design
        qr_image = qr_image.resize((qr_size, qr_size), Image.Resampling.LANCZOS)
        qr_x = (canvas_width - qr_size) // 2

        # Paste QR code
        card.paste(qr_image, (qr_x, qr_y))

        # 7. Add USD KHQR logo overlay in center from URL
        try:
            # Create USD KHQR logo overlay (since SVG handling is complex, we'll create a styled version)
            overlay_size = 40 * scale_factor  # From CSS .qrcode-logo width: 40px

            # Create white background circle for logo
            bg_size = overlay_size + 10 * scale_factor
            overlay_x = qr_x + (qr_size - overlay_size) // 2
            overlay_y = qr_y + (qr_size - overlay_size) // 2
            bg_x = overlay_x - 5 * scale_factor
            bg_y = overlay_y - 5 * scale_factor
            draw.ellipse([bg_x, bg_y, bg_x + bg_size, bg_y + bg_size], fill='white', outline='#ddd', width=2*scale_factor)

            # Create styled "USD KHQR" text to match the logo
            text_font_size = 8 * scale_factor
            try:
                text_font = ImageFont.truetype("arial.ttf", text_font_size)
            except:
                text_font = company_font

            # Draw "USD" in red
            draw.text((overlay_x + 2*scale_factor, overlay_y + 8*scale_factor), "USD", fill='#E1232E', font=text_font)
            # Draw "KHQR" in red below
            draw.text((overlay_x + 2*scale_factor, overlay_y + 20*scale_factor), "KHQR", fill='#E1232E', font=text_font)

        except:
            # Fallback: simple white circle with text
            overlay_size = 40 * scale_factor
            overlay_x = qr_x + (qr_size - overlay_size) // 2
            overlay_y = qr_y + (qr_size - overlay_size) // 2
            draw.ellipse([overlay_x, overlay_y, overlay_x + overlay_size, overlay_y + overlay_size], fill='white', outline='#ccc')
            draw.text((overlay_x + 8*scale_factor, overlay_y + 15*scale_factor), "KHQR", fill='black', font=company_font)

        # 8. Add bank logos at bottom (bottom: -40px, width: 96px from CSS .qrcode-banklogo) - scaled
        try:
            bank_logos = Image.open("payment_icons-cd5e952dde3b886dea1fd1b983d43ce372f1692dec253808ec654096d2feb701-200h.png")
            bank_width = 96 * scale_factor  # From CSS --dl-size-size-medium: 96px
            bank_height = int(bank_logos.height * (bank_width / bank_logos.width))
            bank_logos = bank_logos.resize((bank_width, bank_height), Image.Resampling.LANCZOS)

            # Position at bottom (bottom: -40px means 40px below the card)
            bank_x = (canvas_width - bank_width) // 2
            bank_y = canvas_height - 40 * scale_factor  # This will be cut off, but matches CSS

            # Convert and paste
            if bank_logos.mode != 'RGBA':
                bank_logos = bank_logos.convert('RGBA')

            # Only paste if it fits within the card
            if bank_y >= 0:
                card.paste(bank_logos, (bank_x, bank_y), bank_logos)
        except:
            pass  # Skip bank logos if file not found

        # 9. Paste the card onto the background canvas with shadow effect
        # Create shadow effect (scaled)
        shadow_offset = 5 * scale_factor
        shadow = Image.new('RGBA', (canvas_width + shadow_offset*2, canvas_height + shadow_offset*2), (0, 0, 0, 30))

        # Paste shadow first
        bg_canvas.paste(shadow, (20*scale_factor + shadow_offset, 40*scale_factor + shadow_offset), shadow)

        # Paste main card
        bg_canvas.paste(card, (20*scale_factor, 40*scale_factor), card)

        # Convert to RGB for better compatibility and resize to reasonable size for Telegram
        final_image = Image.new('RGB', bg_canvas.size, (217, 217, 217))
        final_image.paste(bg_canvas, (0, 0), bg_canvas)

        # Resize to a reasonable size for Telegram while maintaining quality
        # Keep it high resolution but not too large for messaging
        final_width = canvas_width // 2  # Half the scale for reasonable file size
        final_height = int(final_image.height * (final_width / final_image.width))
        final_image = final_image.resize((final_width, final_height), Image.Resampling.LANCZOS)

        # Convert to bytes for sending with high quality
        img_byte_arr = io.BytesIO()
        final_image.save(img_byte_arr, format='PNG', quality=100, optimize=True)
        img_byte_arr.seek(0)

        return img_byte_arr

    except Exception as e:
        print(f"Error creating enhanced QR design: {e}")
        # Fallback to original QR code
        qr_response = requests.get(qr_url)
        return io.BytesIO(qr_response.content)

# Auto-check transaction status
def auto_check_transaction(user_id, md5, amount):
    payload = {"md5": md5}
    headers = {
        'Content-Type': 'application/json',
        'Authorization': BAKONG_AUTH_TOKEN
    }

    check_count = 0

    while check_count < MAX_CHECKS and user_id in transactions:
        try:
            response = requests.post(BAKONG_API_URL, json=payload, headers=headers)
            if response.status_code in [200, 201]:
                response_data = response.json()
                if response_data.get('responseCode') == 0:  # Success
                    # Send success message with emoji
                    success_message = f"""
✅ Payment Confirmed! ✅

💰 Amount: ${amount} USD
✨ Status: Successfully processed
🎉 Thank you for your payment!

Your account has been updated.
"""
                    bot.send_message(user_id, success_message)

                    # Delete the QR code message
                    if user_id in transactions:
                        try:
                            bot.delete_message(
                                chat_id=user_id,
                                message_id=transactions[user_id]["message_id"]
                            )
                        except:
                            pass  # Message might already be deleted
                        transactions.pop(user_id, None)
                    return

            time.sleep(CHECK_INTERVAL)  # Check every 10 seconds
            check_count += 1

        except Exception as e:
            print(f"Error checking transaction: {e}")
            time.sleep(CHECK_INTERVAL)
            check_count += 1

# Delete QR code after timeout
def delete_qr_after_timeout(user_id):
    time.sleep(QR_CODE_TIMEOUT)  # Wait for 3 minutes
    transaction = transactions.get(user_id)
    if transaction:
        try:
            bot.delete_message(chat_id=user_id, message_id=transaction["message_id"])
        except:
            pass  # Message might already be deleted

        transactions.pop(user_id, None)

        timeout_message = """
⏰ QR Code Expired

Your QR code has expired after 3 minutes.
Please use /topup again to generate a new payment code.

Example: /topup 0.01
"""
        bot.send_message(user_id, timeout_message)

# Handle any other messages
@bot.message_handler(func=lambda msg: True)
def handle_other_messages(message):
    help_text = """
❓ Unknown command!

Available commands:
💰 /topup <amount> - Generate payment QR code
ℹ️ /help - Show help information
🏠 /start - Show welcome message

Example: /topup 0.01
"""
    bot.reply_to(message, help_text)

# Start the bot
if __name__ == "__main__":
    print("🤖 Payment Bot is starting...")
    print("📱 Bot is ready to receive payments!")
    try:
        bot.infinity_polling()
    except Exception as e:
        print(f"❌ Bot error: {e}")
        print("🔄 Restarting bot...")
