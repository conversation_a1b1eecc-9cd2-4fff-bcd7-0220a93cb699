import telebot
import requests
import time
import threading
from PIL import Image, ImageDraw, ImageFont
import qrcode
import io
import os
from datetime import datetime
from config import *

# Bot configuration
bot = telebot.TeleBot(BOT_TOKEN)

# Store active transactions
transactions = {}

# Welcome message and commands
@bot.message_handler(commands=['start'])
def send_welcome(message):
    welcome_text = """
🎉 Welcome to Payment Bot! 🎉

Available commands:
💰 /topup - Add money to your account
ℹ️ /help - Show this help message

Simply use /topup followed by the amount you want to add!
Example: /topup 0.01
"""
    bot.reply_to(message, welcome_text)

@bot.message_handler(commands=['help'])
def send_help(message):
    help_text = """
📋 Bot Commands:

💰 /topup - Start the payment process
   Usage: /topup <amount>
   Example: /topup 0.01

🔄 The bot will:
1. Generate a QR code for payment
2. Monitor the transaction automatically
3. Confirm when payment is received
4. QR codes expire after 3 minutes

Need help? Contact support!
"""
    bot.reply_to(message, help_text)

@bot.message_handler(commands=['topup'])
def handle_topup(message):
    try:
        # Extract amount from command
        command_parts = message.text.split()
        if len(command_parts) < 2:
            bot.reply_to(message, "Please specify an amount!\nExample: /topup 0.01")
            return
        
        amount = command_parts[1].strip()
        
        # Validate amount
        try:
            float_amount = float(amount)
            if float_amount <= 0:
                bot.reply_to(message, "Amount must be greater than 0!")
                return
        except ValueError:
            bot.reply_to(message, "Invalid amount! Please enter a numeric value.\nExample: /topup 0.01")
            return
        
        # Generate QR code
        generate_qr_code(message, amount)
        
    except Exception as e:
        bot.reply_to(message, f"An error occurred: {e}")

def generate_qr_code(message, amount):
    try:
        # Set up the API URL with the user-provided amount
        api_url = f"{KHQR_API_BASE_URL}?amount={amount}&bakongid={BAKONG_ID}&merchantname={MERCHANT_NAME}"
        
        # Make the GET request to the API
        response = requests.get(api_url)
        
        if response.status_code in [200, 201]:
            response_data = response.json()
            qr_code_url = response_data.get('qr')
            md5 = response_data.get('md5')
            
            if qr_code_url and md5:
                # Create enhanced QR code design
                enhanced_qr_image = create_enhanced_qr_design(qr_code_url, amount)
                
                # Send the enhanced QR code image
                caption = f"""
💳 Payment QR Code Generated

💰 Amount: ${amount} USD
⏰ Expires in: 3 minutes
🔄 Status: Waiting for payment...

Scan the QR code above to complete your payment.
"""
                
                msg = bot.send_photo(
                    message.chat.id, 
                    enhanced_qr_image, 
                    caption=caption
                )
                
                # Store the transaction
                transactions[message.chat.id] = {
                    "md5": md5,
                    "message_id": msg.message_id,
                    "timestamp": time.time(),
                    "amount": amount
                }
                
                # Start automatic checking
                threading.Thread(
                    target=auto_check_transaction, 
                    args=(message.chat.id, md5, amount)
                ).start()
                
                # Start a timer to delete the QR code after 3 minutes
                threading.Thread(
                    target=delete_qr_after_timeout, 
                    args=(message.chat.id,)
                ).start()
            else:
                bot.reply_to(message, "❌ Failed to generate QR code: Missing QR code URL or MD5 in response.")
        else:
            bot.reply_to(message, f"❌ Failed to make the request. Status code: {response.status_code}")
    except Exception as e:
        bot.reply_to(message, f"❌ An error occurred: {e}")

def create_enhanced_qr_design(qr_url, amount):
    try:
        # Download the QR code image
        qr_response = requests.get(qr_url)
        qr_image = Image.open(io.BytesIO(qr_response.content))
        
        # Create a new image with enhanced design
        canvas = Image.new('RGB', (CANVAS_WIDTH, CANVAS_HEIGHT), 'white')
        draw = ImageDraw.Draw(canvas)

        # Draw header background
        draw.rectangle([0, 0, CANVAS_WIDTH, HEADER_HEIGHT], fill=HEADER_COLOR)
        
        # Add title text
        try:
            title_font = ImageFont.truetype("arial.ttf", 24)
            subtitle_font = ImageFont.truetype("arial.ttf", 16)
        except:
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
        
        # Title
        title_text = "Payment QR Code"
        title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        draw.text(
            ((CANVAS_WIDTH - title_width) // 2, 20),
            title_text,
            fill='white',
            font=title_font
        )

        # Amount
        amount_text = f"Amount: ${amount} USD"
        amount_bbox = draw.textbbox((0, 0), amount_text, font=subtitle_font)
        amount_width = amount_bbox[2] - amount_bbox[0]
        draw.text(
            ((CANVAS_WIDTH - amount_width) // 2, 50),
            amount_text,
            fill='white',
            font=subtitle_font
        )

        # Resize and center QR code
        qr_image = qr_image.resize((QR_SIZE, QR_SIZE), Image.Resampling.LANCZOS)
        qr_x = (CANVAS_WIDTH - QR_SIZE) // 2
        qr_y = HEADER_HEIGHT + 50
        canvas.paste(qr_image, (qr_x, qr_y))
        
        # Add footer information
        footer_y = qr_y + QR_SIZE + 30
        
        # Instructions
        instructions = [
            "1. Open your banking app",
            "2. Scan this QR code",
            "3. Confirm the payment",
            "4. Wait for confirmation"
        ]
        
        for i, instruction in enumerate(instructions):
            draw.text(
                (50, footer_y + i * 25), 
                instruction, 
                fill='#333333', 
                font=subtitle_font
            )
        
        # Add timestamp
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        timestamp_text = f"Generated: {timestamp}"
        draw.text(
            (50, footer_y + len(instructions) * 25 + 20), 
            timestamp_text, 
            fill='#666666', 
            font=subtitle_font
        )
        
        # Convert to bytes for sending
        img_byte_arr = io.BytesIO()
        canvas.save(img_byte_arr, format='PNG')
        img_byte_arr.seek(0)
        
        return img_byte_arr

    except Exception as e:
        print(f"Error creating enhanced QR design: {e}")
        # Fallback to original QR code
        qr_response = requests.get(qr_url)
        return io.BytesIO(qr_response.content)

# Auto-check transaction status
def auto_check_transaction(user_id, md5, amount):
    payload = {"md5": md5}
    headers = {
        'Content-Type': 'application/json',
        'Authorization': BAKONG_AUTH_TOKEN
    }

    check_count = 0

    while check_count < MAX_CHECKS and user_id in transactions:
        try:
            response = requests.post(BAKONG_API_URL, json=payload, headers=headers)
            if response.status_code in [200, 201]:
                response_data = response.json()
                if response_data.get('responseCode') == 0:  # Success
                    # Send success message with emoji
                    success_message = f"""
✅ Payment Confirmed! ✅

💰 Amount: ${amount} USD
✨ Status: Successfully processed
🎉 Thank you for your payment!

Your account has been updated.
"""
                    bot.send_message(user_id, success_message)

                    # Delete the QR code message
                    if user_id in transactions:
                        try:
                            bot.delete_message(
                                chat_id=user_id,
                                message_id=transactions[user_id]["message_id"]
                            )
                        except:
                            pass  # Message might already be deleted
                        transactions.pop(user_id, None)
                    return

            time.sleep(CHECK_INTERVAL)  # Check every 10 seconds
            check_count += 1

        except Exception as e:
            print(f"Error checking transaction: {e}")
            time.sleep(CHECK_INTERVAL)
            check_count += 1

# Delete QR code after timeout
def delete_qr_after_timeout(user_id):
    time.sleep(QR_CODE_TIMEOUT)  # Wait for 3 minutes
    transaction = transactions.get(user_id)
    if transaction:
        try:
            bot.delete_message(chat_id=user_id, message_id=transaction["message_id"])
        except:
            pass  # Message might already be deleted

        transactions.pop(user_id, None)

        timeout_message = """
⏰ QR Code Expired

Your QR code has expired after 3 minutes.
Please use /topup again to generate a new payment code.

Example: /topup 0.01
"""
        bot.send_message(user_id, timeout_message)

# Handle any other messages
@bot.message_handler(func=lambda message: True)
def handle_other_messages(message):
    help_text = """
❓ Unknown command!

Available commands:
💰 /topup <amount> - Generate payment QR code
ℹ️ /help - Show help information
🏠 /start - Show welcome message

Example: /topup 0.01
"""
    bot.reply_to(message, help_text)

# Start the bot
if __name__ == "__main__":
    print("🤖 Payment Bot is starting...")
    print("📱 Bot is ready to receive payments!")
    try:
        bot.infinity_polling()
    except Exception as e:
        print(f"❌ Bot error: {e}")
        print("🔄 Restarting bot...")
