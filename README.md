# Telegram Payment Bot

A Telegram bot that generates QR codes for payments using the KHQR (Khmer QR) system and automatically monitors payment status.

## Features

- 💰 Generate payment QR codes with custom amounts
- 🎨 Enhanced QR code design with branding
- ⏰ Automatic payment monitoring
- ✅ Real-time payment confirmation
- 🔄 Auto-expiring QR codes (3 minutes)
- 📱 User-friendly interface with emojis

## Setup Instructions

### 1. Prerequisites

- Python 3.7 or higher
- A Telegram Bot Token (get from [@BotFather](https://t.me/BotFather))

### 2. Installation

1. Clone or download this repository
2. Install required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### 3. Configuration

1. Open `config.py` file
2. Replace `YOUR_BOT_TOKEN_HERE` with your actual Telegram bot token:
   ```python
   BOT_TOKEN = "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz"
   ```

### 4. Running the Bot

```bash
python payment_bot.py
```

The bot will start and display:
```
🤖 Payment Bot is starting...
📱 Bo<PERSON> is ready to receive payments!
```

## Usage

### Commands

- `/start` - Welcome message and available commands
- `/help` - Show help information
- `/topup <amount>` - Generate payment QR code

### Example Usage

1. Start a conversation with your bot
2. Send `/topup 0.01` to generate a QR code for $0.01
3. The bot will send an enhanced QR code image
4. Scan the QR code with your banking app
5. The bot will automatically confirm payment when received

## Bot Features

### Enhanced QR Code Design

The bot creates visually appealing QR codes with:
- Header with payment information
- Centered QR code
- Step-by-step instructions
- Timestamp
- Professional styling

### Automatic Payment Monitoring

- Checks payment status every 10 seconds
- Monitors for 3 minutes maximum
- Sends confirmation when payment is received
- Automatically cleans up expired QR codes

### Error Handling

- Validates payment amounts
- Handles API errors gracefully
- Provides clear error messages
- Fallback to basic QR code if design fails

## Configuration Options

Edit `config.py` to customize:

- `QR_CODE_TIMEOUT`: How long QR codes remain valid (default: 180 seconds)
- `CHECK_INTERVAL`: How often to check payment status (default: 10 seconds)
- `CANVAS_WIDTH/HEIGHT`: QR code image dimensions
- `HEADER_COLOR`: Header background color

## API Integration

The bot integrates with:
- **KHQR API**: For generating QR codes
- **Bakong API**: For checking payment status

## Troubleshooting

### Common Issues

1. **Bot not responding**
   - Check if the bot token is correct
   - Ensure the bot is started with `/start` command

2. **QR code generation fails**
   - Check internet connection
   - Verify API endpoints are accessible

3. **Payment not detected**
   - Ensure the payment amount matches exactly
   - Check if payment was made within 3 minutes

### Logs

The bot prints status messages to console:
- Startup confirmation
- Error messages
- Transaction checking status

## Security Notes

- Keep your bot token secure
- Don't share the `config.py` file with sensitive information
- The bot only monitors payments, it doesn't handle actual money

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review bot logs for error messages
3. Ensure all dependencies are installed correctly

## License

This project is for educational purposes. Please ensure compliance with local regulations when handling payments.
